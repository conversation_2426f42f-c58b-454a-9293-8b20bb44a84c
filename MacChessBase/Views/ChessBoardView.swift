//
//  ChessBoardView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/13.
//

import SwiftUI
import ChessKit
import AppKit

/// A pure chess board view that handles only board display and drag interactions
/// This view is decoupled from game notation and other UI components
struct ChessBoardView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    @ObservedObject var dragManager: ChessDragManager  // Direct observation for UI updates
    @StateObject private var dragState = DragState()
    @State private var previewArrow: (start: Square, end: Square)?

    let boardSize: CGFloat

    // MARK: - Initializer
    init(viewModel: ChessGameViewModel, boardSize: CGFloat) {
        self.viewModel = viewModel
        self.dragManager = viewModel.dragManager  // Observe the same instance
        self.boardSize = boardSize
    }
    
    // MARK: - Constants
    private let minBoardSize: CGFloat = 320
    private let maxBoardSize: CGFloat = 800
    
    var body: some View {
        boardView()
    }
    
    // MARK: - Board View
    @ViewBuilder
    private func boardView(
        showHighlights: Bool = true,
        showVisualAnnotations: Bool = true,
        enableInteractions: Bool = true
    ) -> some View {
        let squareSize = boardSize / 8
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.session.isBoardFlipped)
        
        ZStack {
            // Chess board squares
            createBoardGrid(
                transformer: transformer,
                squareSize: squareSize,
                showHighlights: showHighlights,
                enableInteractions: enableInteractions
            )
            .contextMenu {
                Button(action: {
                    viewModel.toggleBoardFlip()
                }) {
                    Label("Flip Board", systemImage: "rotate.3d")
                }
                
                Divider()
                
                Button(action: {
                    viewModel.openPositionEditor()
                }) {
                    Label("Set Up Position", systemImage: "square.grid.3x3.square")
                }
                
                Divider()
                
                Menu {
                    Button(action: {
                        captureBoardToClipboard(includeHighlights: false)
                    }) {
                        Label("Clean Board", systemImage: "camera.fill")
                    }
                    
                    Button(action: {
                        captureBoardToClipboard(includeHighlights: true)
                    }) {
                        Label("With Highlights", systemImage: "camera.badge.ellipsis")
                    }
                } label: {
                    Label("Copy Board Screenshot", systemImage: "camera")
                }
                
                Menu {
                    Button(action: {
                        saveBoardScreenshot(includeHighlights: false)
                    }) {
                        Label("Clean Board", systemImage: "square.and.arrow.down.fill")
                    }
                    
                    Button(action: {
                        saveBoardScreenshot(includeHighlights: true)
                    }) {
                        Label("With Highlights", systemImage: "square.and.arrow.down.trianglebadge.exclamationmark")
                    }
                } label: {
                    Label("Save Board Screenshot", systemImage: "square.and.arrow.down")
                }
            }
            
            // Visual annotations overlay (square highlights and arrows)
            if showVisualAnnotations,
               let currentMove = viewModel.session.game.moves.getNodeMove(index: viewModel.session.currentMoveIndex),
               !currentMove.positionComment.visualAnnotations.isEmpty {
                VisualAnnotationsView(
                    visualAnnotations: currentMove.positionComment.visualAnnotations,
                    squareSize: squareSize,
                    isFlipped: viewModel.session.isBoardFlipped
                )
            }
            
            // Preview annotation overlay
            if showVisualAnnotations && enableInteractions,
               let previewArrow = previewArrow {
                let previewAnnotationArrow = Move.VisualAnnotations.Arrow(
                    color: viewModel.session.currentAnnotationColor, from: previewArrow.start,
                    to: previewArrow.end
                )
                let previewAnnotation = Move.VisualAnnotations(arrows: [previewAnnotationArrow])
                VisualAnnotationsView(
                    visualAnnotations: previewAnnotation,
                    squareSize: squareSize,
                    isFlipped: viewModel.session.isBoardFlipped
                )
            }
            
            // Floating dragged piece - uses independent drag state
            if let draggedPiece = dragState.draggedPiece {
                DraggedPieceView(
                    piece: draggedPiece.piece,
                    position: dragState.dragPosition,
                    squareSize: squareSize
                )
            }
            
            // Floating reverse dragged piece (opponent piece being dragged)
            if let reverseDraggedPiece = dragState.reverseDraggedPiece {
                // Only show the dragged piece if there's actually a piece to drag
                // (don't show virtual pieces created for empty square reverse drag)
                if viewModel.session.board.position.piece(at: reverseDraggedPiece.fromSquare) != nil {
                    DraggedPieceView(
                        piece: reverseDraggedPiece.piece,
                        position: dragState.reverseDragPosition,
                        squareSize: squareSize
                    )
                }
            }
        }
        .frame(width: boardSize, height: boardSize)
        .aspectRatio(1, contentMode: .fit)
        .clipped()
        .coordinateSpace(name: "chessBoard")
        .onTapGesture { location in
            // Cancel drag if tapping outside squares while dragging
            if dragState.draggedPiece != nil || dragState.reverseDraggedPiece != nil {
                dragState.cancelDrag()
                viewModel.clearAllSelections()
                NSCursor.arrow.set()
            }
        }
        .onContinuousHover { phase in
            // Only handle global board hover when not over pieces
            // Individual squares handle their own hover cursors
            switch phase {
            case .active(_):
                // Don't override square-level cursor management
                break
            case .ended:
                // Only reset if we're in a drag state and leaving the board
                if dragState.draggedPiece != nil {
                    NSCursor.arrow.set()
                }
            }
        }
    }
    
    // MARK: - Drag Handling
    
    /// Handles the start of a drag operation
    private func handleDragStart(piece: Piece, from square: Square, at position: CGPoint) {
        // Allow drag for any piece, validation will be done when attempting the move
        // Start drag in local state
        dragState.startDrag(piece: piece, from: square, at: position)
        
        // Update viewModel selection for move validation
        viewModel.setSelectedSquare(square)
        
        // Cursor is now handled in OptimizedChessSquareView based on piece moveability
    }
    
    /// Handles the end of a drag operation
    private func handleDragEnd(at targetSquare: Square, transformer: BoardCoordinateTransformer) {
        guard let draggedPiece = dragState.draggedPiece else { return }
        
        // Attempt move through viewModel
        viewModel.attemptMove(from: draggedPiece.fromSquare, to: targetSquare)
        
        // Clear local drag state
        dragState.endDrag()
        
        // Cursor is now handled in OptimizedChessSquareView
    }
    
    // MARK: - Reverse Drag Handling
    
    /// Handles the start of a reverse drag operation (from target to source)
    private func handleReverseDragStart(from startSquare: Square, at position: CGPoint) -> Bool {
        let success = viewModel.startReverseDrag(from: startSquare)
        if success {
            // For reverse drag, we create a virtual drag state even for empty squares
            if let piece = viewModel.session.board.position.piece(at: startSquare) {
                // Start reverse drag animation for the opponent piece
                dragState.startReverseDrag(piece: piece, from: startSquare, at: position)
            } else {
                // For empty squares, create a virtual piece just for drag state tracking
                // This piece won't be rendered, but helps maintain drag state consistency
                let virtualPiece = Piece(.pawn, color: viewModel.session.board.position.sideToMove.opposite, square: startSquare)
                dragState.startReverseDrag(piece: virtualPiece, from: startSquare, at: position)
            }
        }
        return success
    }
    
    /// Handles the end of a reverse drag operation
    private func handleReverseDragEnd(to endSquare: Square) {
        dragState.endReverseDrag()
        viewModel.completeReverseDrag(to: endSquare)
    }
    
    // MARK: - Board View Helpers
    
    @ViewBuilder
    private func createBoardGrid(
        transformer: BoardCoordinateTransformer,
        squareSize: CGFloat,
        showHighlights: Bool,
        enableInteractions: Bool
    ) -> some View {
        VStack(spacing: 0) {
            // Dynamic rank labels based on flip state
            ForEach(transformer.rankLabels, id: \.self) { rank in
                createBoardRank(
                    rank: rank.description,
                    transformer: transformer,
                    squareSize: squareSize,
                    showHighlights: showHighlights,
                    enableInteractions: enableInteractions
                )
            }
        }
        .drawingGroup() // Hardware accelerate the entire board
    }
    
    @ViewBuilder
    private func createBoardRank(
        rank: String,
        transformer: BoardCoordinateTransformer,
        squareSize: CGFloat,
        showHighlights: Bool,
        enableInteractions: Bool
    ) -> some View {
        HStack(spacing: 0) {
            // Dynamic file labels based on flip state
            ForEach(transformer.fileLabels, id: \.self) { file in
                createChessSquare(
                    file: file,
                    rank: rank,
                    transformer: transformer,
                    squareSize: squareSize,
                    showHighlights: showHighlights,
                    enableInteractions: enableInteractions
                )
            }
        }
    }
    
    @ViewBuilder
    private func createChessSquare(
        file: String,
        rank: String,
        transformer: BoardCoordinateTransformer,
        squareSize: CGFloat,
        showHighlights: Bool,
        enableInteractions: Bool
    ) -> some View {
        let square = Square("\(file)\(rank)")
        
        OptimizedChessSquareView(
            square: square,
            piece: viewModel.session.board.position.piece(at: square),
            isSelected: showHighlights ? (dragManager.selectedSquare == square) : false,
            isPossibleMove: showHighlights ? dragManager.possibleMoves.contains(square) : false,
            isLastMove: showHighlights ? isLastMoveSquare(square) : false,
            onSquarePressed: enableInteractions ? { viewModel.handleSquarePress(square) } : { },
            squareSize: squareSize,
            currentPlayer: viewModel.session.board.position.sideToMove,
            isDraggedFrom: enableInteractions ? dragState.isDraggedFrom(square) : false,
            isReverseDraggedFrom: enableInteractions ? dragState.isReverseDraggedFrom(square) : false,
            onDragStart: enableInteractions ? { piece, square, position in
                handleDragStart(piece: piece, from: square, at: position)
            } : { _, _, _ in },
            onDragUpdate: enableInteractions ? { position in
                dragState.updateDrag(to: position)
            } : { _ in },
            onReverseDragUpdate: enableInteractions ? { position in
                dragState.updateReverseDrag(to: position)
            } : { _ in },
            onDragEnd: enableInteractions ? { targetSquare in
                handleDragEnd(at: targetSquare, transformer: transformer)
            } : { _ in },
            onReverseDragStart: enableInteractions ? { startSquare, position in
                return handleReverseDragStart(from: startSquare, at: position)
            } : { _, _ in false },
            onReverseDragEnd: enableInteractions ? { endSquare in
                handleReverseDragEnd(to: endSquare)
            } : { _ in },
            canMovePiece: enableInteractions ? viewModel.canMovePiece : { _ in false },
            isReverseDragTarget: enableInteractions ? (dragManager.isReverseDragActive && dragManager.reverseDragTarget == square) : false,
            isReverseDragSource: enableInteractions ? (dragManager.isReverseDragActive && dragManager.reverseDragValidSources.contains(square)) : false,
            onAnnotationEdit: enableInteractions ? { _ in viewModel.toggleSquareHighlight(at: square) } : nil,
            onAnnotationDrag: enableInteractions ? { fromSquare, toSquare in
                viewModel.toggleArrow(from: fromSquare, to: toSquare)
                self.previewArrow = nil
            } : nil,
            onAnnotationDragUpdate: enableInteractions ? { fromSquare, toSquare in
                if fromSquare != toSquare {
                    self.previewArrow = (fromSquare, toSquare)
                } else {
                    self.previewArrow = nil
                }
            } : nil
        )
        .frame(width: squareSize, height: squareSize)
    }
    
    // MARK: - Helper Methods
    
    private func isLastMoveSquare(_ square: Square) -> Bool {
        guard let lastMetaMove = viewModel.lastMove?.metaMove else { return false }
        return lastMetaMove.start == square || lastMetaMove.end == square
    }
    
    // MARK: - Screenshot Methods
    
    /// Captures the chess board as an image and copies it to clipboard
    private func captureBoardToClipboard(includeHighlights: Bool = false) {
        Task { @MainActor in
            guard let image = captureChessBoard(includeHighlights: includeHighlights) else {
                print("Failed to capture chess board image")
                return
            }
            
            let mode = includeHighlights ? "with highlights" : "clean"
            copyImageToClipboard(image, mode: mode)
        }
    }
    
    /// Captures the chess board as an image and saves it to a file
    private func saveBoardScreenshot(includeHighlights: Bool = false) {
        Task { @MainActor in
            guard let image = captureChessBoard(includeHighlights: includeHighlights) else {
                print("Failed to capture chess board image")
                return
            }
            
            let mode = includeHighlights ? "highlights" : "clean"
            saveImageToFile(image, mode: mode)
        }
    }
    
    /// Creates a chess board image with or without highlights
    @MainActor
    private func captureChessBoard(includeHighlights: Bool = false) -> NSImage? {
        // Use the parameterized boardView method
        let screenshotView = boardView(
            showHighlights: includeHighlights,
            showVisualAnnotations: includeHighlights,
            enableInteractions: false // Always disable interactions for screenshots
        )
        
        // Add light background and padding for better presentation
        let screenshotWithBackground = VStack {
            screenshotView
        }
        
        .background(
            Rectangle()
                .fill(Color(.white.opacity(0.9))) // Light background color with square corners
        )
        .padding(10) // Additional outer padding for clean edges
        
        let renderer = ImageRenderer(content: screenshotWithBackground)
        renderer.scale = 2.0 // High DPI for crisp output
        
        return renderer.nsImage
    }
    
    /// Copies the image to system clipboard
    private func copyImageToClipboard(_ image: NSImage, mode: String) {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.writeObjects([image])
        
        print("Chess board screenshot (\(mode)) copied to clipboard")
    }
    
    /// Saves the image to a file using file dialog
    private func saveImageToFile(_ image: NSImage, mode: String) {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.png, .jpeg]
        
        let timestamp = Int(Date().timeIntervalSince1970)
        savePanel.nameFieldStringValue = "ChessBoard_\(mode)_\(timestamp).png"
        savePanel.title = "Save Chess Board Screenshot (\(mode.capitalized))"
        savePanel.message = "Choose a location to save the chess board screenshot"
        
        savePanel.begin { response in
            guard response == .OK, let url = savePanel.url else { return }
            
            // Convert NSImage to PNG data
            guard let tiffData = image.tiffRepresentation,
                  let bitmapImage = NSBitmapImageRep(data: tiffData),
                  let pngData = bitmapImage.representation(using: .png, properties: [:]) else {
                print("Failed to convert image to PNG data")
                return
            }
            
            // Write to file
            do {
                try pngData.write(to: url)
                print("Chess board screenshot saved to: \(url.path)")
            } catch {
                print("Failed to save screenshot: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Preview
#Preview {
    let session = GameSession()
    ChessBoardView(
        viewModel: ChessGameViewModel(session: session),
        boardSize: 400
    )
}
