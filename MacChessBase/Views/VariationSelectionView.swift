//
//  VariationSelectionView.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import SwiftUI
import ChessKit

/// A view that allows users to select from available variations when multiple branches exist
struct VariationSelectionView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    @State private var selectedIndex: Int = 0
    @FocusState private var isFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 12) {
                Text("Select Variation")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                // Keyboard shortcuts help text
                HStack(spacing: 12) {
                    HStack(spacing: 3) {
                        Text("↑↓")
                            .font(.system(.caption2, design: .monospaced))
                            .foregroundColor(.blue.opacity(0.8))
                            .padding(.horizontal, 4)
                            .padding(.vertical, 1)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(3)
                        Text("Select")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack(spacing: 3) {
                        Text("→")
                            .font(.system(.caption2, design: .monospaced))
                            .foregroundColor(.green.opacity(0.8))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 1)
                            .background(Color.green.opacity(0.1))
                            .cornerRadius(3)
                        Text("Confirm")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack(spacing: 3) {
                        Text("←")
                            .font(.system(.caption2, design: .monospaced))
                            .foregroundColor(.orange.opacity(0.8))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 1)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(3)
                        Text("Cancel")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
        
                }
                .opacity(0.9)
            }
            .padding(.top, 24)
            .padding(.horizontal, 24)
            
            Divider()
                .padding(.vertical, 16)
            
            // Variation options
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(Array(viewModel.availableVariations.enumerated()), id: \.element.id) { index, option in
                            VariationOptionRow(
                                option: option,
                                isSelected: index == selectedIndex,
                                onSelect: {
                                    viewModel.selectVariation(option)
                                }
                            )
                            .id(index)
                        }
                    }
                    .padding(.horizontal, 16)
                }
                .onChange(of: selectedIndex) { _, newIndex in
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo(newIndex, anchor: .center)
                    }
                }
            }
            
            Divider()
                .padding(.vertical, 16)
            
            // Footer buttons
            HStack(spacing: 12) {
                Button("Cancel") {
                    viewModel.cancelVariationSelection()
                }
                .keyboardShortcut(.escape)
                
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 24)
        }
        .frame(minWidth: 200, maxWidth: 500)
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(12)
        .shadow(radius: 20)
        .focusable()
        .focused($isFocused)
        .focusEffectDisabled()
        .onKeyPress { keyPress in
            switch keyPress.key {
            case .upArrow:
                if selectedIndex > 0 {
                    selectedIndex -= 1
                }
                return .handled
            case .downArrow:
                if selectedIndex < viewModel.availableVariations.count - 1 {
                    selectedIndex += 1
                }
                return .handled
            case .rightArrow:
                // Right arrow confirms selection
                if !viewModel.availableVariations.isEmpty && selectedIndex < viewModel.availableVariations.count {
                    viewModel.selectVariation(viewModel.availableVariations[selectedIndex])
                }
                return .handled
            case .leftArrow:
                // Left arrow cancels selection
                viewModel.cancelVariationSelection()
                return .handled
            default:
                return .ignored
            }
        }
        .onAppear {
            isFocused = true
        }
    }
}

/// A row displaying a single variation option
struct VariationOptionRow: View {
    let option: VariationOption
    let isSelected: Bool
    let onSelect: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: onSelect) {
            HStack(alignment: .top, spacing: 12) {
                // PGN text
                VStack(alignment: .leading, spacing: 4) {
                    Text(option.pgnText)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                        .lineLimit(3)
                }
                
                Spacer()
                
                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .opacity(isHovered ? 1.0 : 0.6)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(backgroundColorForState())
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        borderColorForState(),
                        lineWidth: isSelected ? 2 : 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
    }
    
    private func backgroundColorForState() -> Color {
        if isSelected {
            return Color.blue.opacity(0.2)
        } else if isHovered {
            return Color.blue.opacity(0.1)
        } else {
            return Color.clear
        }
    }
    
    private func borderColorForState() -> Color {
        if isSelected {
            return Color.blue.opacity(0.6)
        } else if isHovered {
            return Color.blue.opacity(0.3)
        } else {
            return Color.gray.opacity(0.2)
        }
    }
}

//#Preview {
//    let viewModel = ChessGameViewModel()
//    
//    // Create some sample variations for preview
//    viewModel.availableVariations = [
//        ChessGameViewModel.VariationOption(
//            index: MoveTree.Index(number: 2, color: .white),
//            pgnText: "2. Nf3 Nc6 3. Bb5",
//            isMainLine: true
//        ),
//        ChessGameViewModel.VariationOption(
//            index: MoveTree.Index(number: 2, color: .white),
//            pgnText: "2. d4 exd4 3. c3",
//            isMainLine: false
//        ),
//        ChessGameViewModel.VariationOption(
//            index: MoveTree.Index(number: 2, color: .white),
//            pgnText: "2. Bc4 Bc5 3. d3",
//            isMainLine: false
//        )
//    ]
//    viewModel.showVariationSelection = true
//    
//    VariationSelectionView(viewModel: viewModel)
//        .frame(width: 500, height: 400)
//}
