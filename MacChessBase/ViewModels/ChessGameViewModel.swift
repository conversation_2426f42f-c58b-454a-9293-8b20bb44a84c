//
//  ChessGameViewModel.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit
import Combine
import AVFoundation

/// Lightweight ViewModel that connects SwiftUI views to ChessGameCoordinator
@MainActor
final class ChessGameViewModel: ObservableObject {
    // MARK: - Coordinator Reference
    @ObservedObject private var coordinator: ChessGameCoordinator
    
    // MARK: - Session Reference (delegated to coordinator)
    var session: GameSession { coordinator.session }

    // MARK: - UI State Access (delegated to coordinator)
    var uiState: ChessUIState { coordinator.uiState }
    
    // MARK: - Drag Properties (delegated to coordinator)
    var selectedSquare: Square? {
        get { coordinator.uiState.selectedSquare }
        set { 
            if let square = newValue {
                coordinator.handleEvent(.selectSquare(square))
            } else {
                coordinator.handleEvent(.clearSelection)
            }
        }
    }

    var possibleMoves: [Square] { coordinator.uiState.possibleMoves }
    var lastMove: Move? { coordinator.uiState.lastMove }

    // MARK: - Reverse Drag Properties (delegated to coordinator)
    var isReverseDragActive: Bool { coordinator.uiState.isReverseDragActive }
    var reverseDragTarget: Square? { coordinator.uiState.reverseDragTarget }
    var reverseDragValidSources: [Square] { coordinator.uiState.reverseDragValidSources }

    // MARK: - Promotion Properties (delegated to coordinator)
    var promotionMove: Move? { coordinator.uiState.promotionMove }
    var showPromotionDialog: Bool { coordinator.uiState.showPromotionDialog }

    // MARK: - Navigation Properties (delegated to coordinator)
    var showVariationSelection: Bool { coordinator.uiState.showVariationSelection }
    var availableVariations: [ChessNavigator.VariationOption] { coordinator.uiState.availableVariations }
    var isKeyboardNavigationDisabled: Bool { coordinator.uiState.isKeyboardNavigationDisabled }

    // MARK: - Variation Creation Properties (delegated to coordinator)
    var showVariationCreationDialog: Bool { coordinator.uiState.showVariationCreationDialog }
    var pendingMove: Move? { coordinator.uiState.pendingMove }
    var pendingMoveFromIndex: MoveTree.MoveIndex? { coordinator.uiState.pendingMoveFromIndex }
    var existingNextMoveIndex: MoveTree.MoveIndex? { coordinator.uiState.existingNextMoveIndex }

    // MARK: - Move Editing Properties (delegated to coordinator)
    var showMoveEditMenu: Bool { coordinator.uiState.showMoveEditMenu }
    var editMenuPosition: CGPoint { coordinator.uiState.editMenuPosition }
    var selectedMoveForEdit: MoveTree.MoveIndex? { coordinator.uiState.selectedMoveForEdit }

    // MARK: - Game Status (delegated to session)
    var gameStatus: GameStatus { session.gameStatus }
    var gameStatusText: String { session.gameStatusText }

    // MARK: - Engine State Access (delegated to coordinator)
    var isEngineRunning: Bool { coordinator.uiState.isEngineRunning }
    var isEngineAnalyzing: Bool { coordinator.uiState.isEngineAnalyzing }
    var isEnginePaused: Bool { coordinator.uiState.isEnginePaused }
    var engineState: EngineState { coordinator.uiState.engineState }
    var currentEngineEvaluation: EngineEvaluation? { coordinator.uiState.currentEngineEvaluation }
    var engineLines: [EngineLine] { coordinator.uiState.engineLines }

    // MARK: - Move Display Properties (delegated to coordinator)
    var cachedMoves: [MoveDisplayItem] { coordinator.uiState.cachedMoves }
    var hasOnlyCurrentMoveChanged: Bool { coordinator.uiState.hasOnlyCurrentMoveChanged }

    // MARK: - File Operations Properties (delegated to coordinator)
    var showPositionEditor: Bool {
        get { coordinator.fileOperationsAccess.showPositionEditor }
        set { coordinator.fileOperationsAccess.showPositionEditor = newValue }
    }
    
    var showImportErrorAlert: Bool {
        get { coordinator.fileOperationsAccess.showImportErrorAlert }
        set { coordinator.fileOperationsAccess.showImportErrorAlert = newValue }
    }

    // MARK: - Initialization
    init(session: GameSession) {
        self.coordinator = ChessGameCoordinator(session: session)
        
        print("🎯 ChessGameViewModel initialized with coordinator")
    }
    
    // MARK: - Public Action Methods
    
    /// Attempts to move a piece from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        coordinator.handleEvent(.attemptMove(from: startSquare, to: endSquare))
    }
    
    /// Selects a square on the board
    func selectSquare(_ square: Square) {
        coordinator.handleEvent(.selectSquare(square))
    }
    
    /// Clears the current selection
    func clearSelection() {
        coordinator.handleEvent(.clearSelection)
    }
    
    /// Clears all selections
    func clearAllSelections() {
        coordinator.handleEvent(.clearAllSelections)
    }
    
    /// Starts reverse drag operation
    func startReverseDrag(to target: Square) {
        coordinator.handleEvent(.startReverseDrag(target))
    }
    
    /// Ends reverse drag operation
    func endReverseDrag() {
        coordinator.handleEvent(.endReverseDrag)
    }
    
    // MARK: - Navigation Methods
    
    /// Goes to the previous move
    func goToPreviousMove() {
        coordinator.handleEvent(.goToPreviousMove)
    }
    
    /// Goes to the next move
    func goToNextMove() {
        coordinator.handleEvent(.goToNextMove)
    }
    
    /// Goes to the first move
    func goToFirstMove() {
        coordinator.handleEvent(.goToFirstMove)
    }
    
    /// Goes to the last move
    func goToLastMove() {
        coordinator.handleEvent(.goToLastMove)
    }
    
    /// Goes to a specific move
    func goToMove(_ index: MoveTree.MoveIndex) {
        coordinator.handleEvent(.navigateToMove(index))
    }
    
    // MARK: - Dialog Methods
    
    /// Shows promotion dialog
    func showPromotionDialog(for move: Move) {
        coordinator.handleEvent(.showPromotionDialog(move))
    }
    
    /// Hides promotion dialog
    func hidePromotionDialog() {
        coordinator.handleEvent(.hidePromotionDialog)
    }
    
    /// Shows variation creation dialog
    func showVariationDialog(for move: Move, from fromIndex: MoveTree.MoveIndex, existing existingIndex: MoveTree.MoveIndex?) {
        coordinator.handleEvent(.showVariationDialog(move, fromIndex, existingIndex))
    }
    
    /// Hides variation creation dialog
    func hideVariationDialog() {
        coordinator.handleEvent(.hideVariationDialog)
    }
    
    /// Shows move edit menu
    func showMoveEditMenu(for index: MoveTree.MoveIndex, at position: CGPoint) {
        coordinator.handleEvent(.showMoveEditMenu(index, position))
    }
    
    /// Hides move edit menu
    func hideMoveEditMenu() {
        coordinator.handleEvent(.hideMoveEditMenu)
    }
    
    // MARK: - Cache Methods
    
    /// Invalidates the move display cache
    func invalidateCache() {
        coordinator.handleEvent(.invalidateCache)
    }
    
    // MARK: - Manager Access Methods (for backward compatibility)
    
    /// Access to drag manager for UI binding
    var dragManager: ChessDragManager { coordinator.dragManagerAccess }
    
    /// Access to navigator for UI binding
    var navigator: ChessNavigator { coordinator.navigatorAccess }
    
    /// Access to move display manager for UI binding
    var moveDisplayManager: MoveDisplayManager { coordinator.moveDisplayManagerAccess }
    
    /// Access to engine manager for UI binding
    func getEngineManager() -> EngineManager { coordinator.engineManagerAccess }
    
    /// Access to file operations for UI binding
    var fileOperations: ChessFileOperations { coordinator.fileOperationsAccess }

    // MARK: - Game Management Methods

    /// Resets the game to the starting position
    func newGame() {
        session.newGame()
        coordinator.handleEvent(.clearSelection)
        coordinator.handleEvent(.hidePromotionDialog)
        coordinator.handleEvent(.hideVariationDialog)
        session.currentFilePath = nil
        coordinator.handleEvent(.invalidateCache)
    }

    /// Gets the current player's turn
    var currentPlayer: Piece.Color {
        session.board.position.sideToMove
    }

    /// Gets the game's PGN representation
    var pgn: String {
        session.pgn
    }

    // MARK: - File Management Methods

    /// Loads a game from PGN
    func loadGame(from pgn: String) {
        fileOperations.loadGame(from: pgn, into: session)
    }

    /// Loads a game from file URL
    func loadGame(from url: URL) {
        fileOperations.loadGame(from: url, into: session)
    }

    /// Saves the current game to file
    func saveGame(to url: URL) {
        fileOperations.saveGame(session, to: url)
    }

    /// Exports the current game as PGN
    func exportPGN() -> String {
        return fileOperations.exportPGN(from: session)
    }

    /// Imports a game from FEN
    func importFEN(_ fen: String) {
        fileOperations.importFEN(fen, into: session)
    }

    /// Exports the current position as FEN
    func exportFEN() -> String {
        return fileOperations.exportFEN(from: session)
    }

    // MARK: - Annotation Methods

    /// Adds a visual annotation to the board
    func addVisualAnnotation(_ annotation: Move.VisualAnnotations.Annotation) {
        // Delegate to coordinator's annotation manager
        // This will need to be implemented in the coordinator
    }

    /// Removes a visual annotation from the board
    func removeVisualAnnotation(_ annotation: Move.VisualAnnotations.Annotation) {
        // Delegate to coordinator's annotation manager
        // This will need to be implemented in the coordinator
    }

    /// Gets all visual annotations for the current position
    func getVisualAnnotations() -> [Move.VisualAnnotations.Annotation] {
        // Delegate to coordinator's annotation manager
        // This will need to be implemented in the coordinator
        return []
    }

    // MARK: - Move Execution Methods

    /// Executes a promotion move
    func executePromotion(to piece: Piece.Kind) {
        coordinator.executePromotion(to: piece)
    }

    /// Executes a variation creation with the specified option
    func executeVariationCreation(option: ChessMoveExecutor.VariationCreationOption) {
        coordinator.executeVariationCreation(option: option)
    }

    // MARK: - Metadata Binding Methods

    /// Creates a binding for a metadata property
    private func metadataBinding<T>(_ keyPath: WritableKeyPath<Game.Metadata, T>) -> Binding<T> {
        Binding(
            get: {
                self.session.game.metadata[keyPath: keyPath]
            },
            set: {
                self.session.game.metadata[keyPath: keyPath] = $0
                self.session.isModified = true
                self.objectWillChange.send()
            }
        )
    }

    // MARK: - Metadata Bindings
    var eventBinding: Binding<String> { metadataBinding(\.event) }
    var siteBinding: Binding<String> { metadataBinding(\.site) }
    var dateBinding: Binding<String> { metadataBinding(\.date) }
    var roundBinding: Binding<String> { metadataBinding(\.round) }
    var whiteBinding: Binding<String> { metadataBinding(\.white) }
    var blackBinding: Binding<String> { metadataBinding(\.black) }
    var whiteEloBinding: Binding<String> { metadataBinding(\.whiteElo) }
    var blackEloBinding: Binding<String> { metadataBinding(\.blackElo) }
    var whiteFideIdBinding: Binding<String> { metadataBinding(\.whiteFideId) }
    var blackFideIdBinding: Binding<String> { metadataBinding(\.blackFideId) }
    var ecoBinding: Binding<String> { metadataBinding(\.eco) }
    var openingBinding: Binding<String> { metadataBinding(\.opening) }
    var timeControlBinding: Binding<String> { metadataBinding(\.timeControl) }
    var notesBinding: Binding<String> { metadataBinding(\.notes) }
    var setUpBinding: Binding<String> { metadataBinding(\.setUp) }
    var fenBinding: Binding<String> { metadataBinding(\.fen) }

    /// Special binding for result field with default value handling
    var resultBinding: Binding<String> {
        Binding(
            get: {
                let result = self.session.game.metadata.result
                return result.isEmpty ? "*" : result
            },
            set: {
                self.session.game.metadata.result = $0
                self.session.isModified = true
                self.objectWillChange.send()
            }
        )
    }

    /// Auto-detects game result based on current game status
    func autoDetectGameResult() {
        // Only update if result is currently unset
        if session.game.metadata.result.isEmpty || session.game.metadata.result == "*" {
            switch gameStatus {
            case .checkmate(let color):
                session.game.metadata.result = color == .white ? "1-0" : "0-1"
            case .stalemate, .draw:
                session.game.metadata.result = "1/2-1/2"
            case .inProgress:
                session.game.metadata.result = "*"
            }
        }
    }
}
