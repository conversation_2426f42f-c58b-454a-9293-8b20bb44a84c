//
//  ChessGameViewModel.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit
import Combine
import AVFoundation

/// Lightweight ViewModel that connects SwiftUI views to various chess managers
@MainActor
final class ChessGameViewModel: ObservableObject {
    // MARK: - Core Dependencies
    let session: GameSession

    // MARK: - Manager References
    @ObservedObject private var dragManager: ChessDragManager
    @ObservedObject private var navigator: ChessNavigator
    @ObservedObject private var moveDisplayManager: MoveDisplayManager
    @ObservedObject private var fileOperations: ChessFileOperations
    private let engineManager: EngineManager

    // MARK: - Drag Properties (delegated to dragManager)
    var selectedSquare: Square? {
        get { dragManager.selectedSquare }
        set {
            if let square = newValue {
                dragManager.selectSquare(square, in: session)
            } else {
                dragManager.clearSelection()
            }
        }
    }

    var possibleMoves: [Square] { dragManager.possibleMoves }
    var lastMove: Move? {
        return session.game.moves.getNodeMove(index: session.currentMoveIndex)
    }

    // MARK: - Reverse Drag Properties (delegated to dragManager)
    var isReverseDragActive: Bool { dragManager.isReverseDragActive }
    var reverseDragTarget: Square? { dragManager.reverseDragTarget }
    var reverseDragValidSources: [Square] { dragManager.reverseDragValidSources }

    // MARK: - Promotion Properties (delegated to moveExecutor)
    @Published var promotionMove: Move?
    @Published var showPromotionDialog: Bool = false

    // MARK: - Navigation Properties (delegated to navigator)
    var showVariationSelection: Bool { navigator.showVariationSelection }
    var availableVariations: [VariationOption] { navigator.availableVariations }
    var isKeyboardNavigationDisabled: Bool { navigator.isKeyboardNavigationDisabled }

    // MARK: - Variation Creation Properties
    @Published var showVariationCreationDialog: Bool = false
    @Published var pendingMove: Move?
    @Published var pendingMoveFromIndex: MoveTree.MoveIndex?
    @Published var existingNextMoveIndex: MoveTree.MoveIndex?

    // MARK: - Move Editing Properties
    @Published var showMoveEditMenu: Bool = false
    @Published var editMenuPosition: CGPoint = .zero
    @Published var selectedMoveForEdit: MoveTree.MoveIndex?

    // MARK: - Game Status (delegated to session)
    var gameStatus: GameStatus { session.gameStatus }
    var gameStatusText: String { session.gameStatusText }

    // MARK: - Engine State Access (delegated to engineManager)
    var isEngineRunning: Bool { engineManager.state != .stopped }
    var isEngineAnalyzing: Bool { engineManager.state == .analyzing }
    var isEnginePaused: Bool { engineManager.state == .paused }
    var engineState: EngineState { engineManager.state }
    var currentEngineEvaluation: EngineEvaluation? { engineManager.currentEvaluation }
    var engineLines: [EngineLine] { engineManager.engineLines }

    // MARK: - Move Display Properties (delegated to moveDisplayManager)
    var cachedMoves: [MoveDisplayItem] { moveDisplayManager.cachedMoves }
    var hasOnlyCurrentMoveChanged: Bool { moveDisplayManager.hasOnlyCurrentMoveChanged }

    // MARK: - File Operations Properties (delegated to fileOperations)
    var showPositionEditor: Bool {
        get { fileOperations.showPositionEditor }
        set { fileOperations.showPositionEditor = newValue }
    }

    var showImportErrorAlert: Bool {
        get { fileOperations.showImportErrorAlert }
        set { fileOperations.showImportErrorAlert = newValue }
    }

    // MARK: - Initialization
    init(session: GameSession) {
        self.session = session
        self.dragManager = ChessDragManager()
        self.navigator = ChessNavigator()
        self.moveDisplayManager = MoveDisplayManager()
        self.fileOperations = ChessFileOperations()
        self.engineManager = EngineManager.shared

        // Set up callbacks for file operations
        fileOperations.onStateReset = { [weak self] in
            self?.clearSelection()
            self?.hidePromotionDialog()
            self?.hideVariationDialog()
        }

        fileOperations.onCacheInvalidate = { [weak self] in
            self?.moveDisplayManager.invalidateCache()
        }

        print("🎯 ChessGameViewModel initialized with direct manager references")
    }
    
    // MARK: - Public Action Methods

    /// Attempts to move a piece from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        // Try to make the move in the session
        if session.makeMove(from: startSquare, to: endSquare) {
            // Clear selection after successful move
            dragManager.clearSelection()

            // Check if promotion is needed
            let piece = session.board.position.piece(at: startSquare)
            if let piece = piece, piece.kind == .pawn {
                let endRank = endSquare.rank
                let isPromotion = (piece.color == .white && endRank == ._8) ||
                                 (piece.color == .black && endRank == ._1)
                if isPromotion {
                    // Get the move that was just made
                    if let move = session.game.moves.getNodeMove(index: session.currentMoveIndex) {
                        showPromotionDialog(for: move)
                        return
                    }
                }
            }

            // Invalidate move display cache
            moveDisplayManager.invalidateCache()
        }
    }

    /// Selects a square on the board
    func selectSquare(_ square: Square) {
        dragManager.selectSquare(square, in: session)
    }

    /// Clears the current selection
    func clearSelection() {
        dragManager.clearSelection()
    }

    /// Clears all selections
    func clearAllSelections() {
        dragManager.clearAllSelections()
    }

    /// Starts reverse drag operation
    func startReverseDrag(from target: Square) -> Bool {
        return dragManager.startReverseDrag(from: target, in: session)
    }

    /// Ends reverse drag operation
    func endReverseDrag() {
        dragManager.cancelReverseDrag()
    }
    
    // MARK: - Navigation Methods

    /// Goes to the previous move
    func goToPreviousMove() {
        navigator.goToPreviousMove(in: session)
        moveDisplayManager.invalidateCache()
    }

    /// Goes to the next move
    func goToNextMove() {
        navigator.goToNextMove(in: session)
        moveDisplayManager.invalidateCache()
    }

    /// Goes to the first move
    func goToFirstMove() {
        navigator.goToStart(in: session)
        moveDisplayManager.invalidateCache()
    }

    /// Goes to the start of the game (alias for goToFirstMove)
    func goToStart() {
        goToFirstMove()
    }

    /// Goes to the last move
    func goToLastMove() {
        navigator.goToEnd(in: session)
        moveDisplayManager.invalidateCache()
    }

    /// Goes to the end of the game (alias for goToLastMove)
    func goToEnd() {
        goToLastMove()
    }

    /// Goes to a specific move
    func goToMove(at index: MoveTree.MoveIndex) {
        session.goToMove(at: index)
        moveDisplayManager.invalidateCache()
    }
    
    // MARK: - Dialog Methods

    /// Shows promotion dialog
    func showPromotionDialog(for move: Move) {
        promotionMove = move
        showPromotionDialog = true
    }

    /// Hides promotion dialog
    func hidePromotionDialog() {
        showPromotionDialog = false
        promotionMove = nil
    }

    /// Shows variation creation dialog
    func showVariationDialog(for move: Move, from fromIndex: MoveTree.MoveIndex, existing existingIndex: MoveTree.MoveIndex?) {
        pendingMove = move
        pendingMoveFromIndex = fromIndex
        existingNextMoveIndex = existingIndex
        showVariationCreationDialog = true
        navigator.isKeyboardNavigationDisabled = true
    }

    /// Hides variation creation dialog
    func hideVariationDialog() {
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
        navigator.isKeyboardNavigationDisabled = false
    }

    /// Shows move edit menu
    func showMoveEditMenu(for index: MoveTree.MoveIndex, at position: CGPoint) {
        selectedMoveForEdit = index
        editMenuPosition = position
        showMoveEditMenu = true
    }

    /// Hides move edit menu
    func hideMoveEditMenu() {
        showMoveEditMenu = false
        selectedMoveForEdit = nil
    }

    // MARK: - Cache Methods

    /// Invalidates the move display cache
    func invalidateCache() {
        moveDisplayManager.invalidateCache()
    }


    // MARK: - Game Management Methods

    /// Resets the game to the starting position
    func newGame() {
        session.newGame()
        clearSelection()
        hidePromotionDialog()
        hideVariationDialog()
        session.currentFilePath = nil
        invalidateCache()
    }

    /// Gets the current player's turn
    var currentPlayer: Piece.Color {
        session.board.position.sideToMove
    }

    /// Gets the game's PGN representation
    var pgn: String {
        session.pgn
    }

    // MARK: - File Management Methods

    /// Loads a game from PGN
    func loadGame(from pgn: String) {
        fileOperations.loadGame(from: pgn, into: session)
    }

    /// Loads a game from file URL
    func loadGame(from url: URL) {
        fileOperations.loadGame(from: url, into: session)
    }

    /// Saves the current game to file
    func saveGame(to url: URL) {
        do {
            try fileOperations.saveGame(to: url, from: session)
        } catch {
            print("Error saving game: \(error)")
        }
    }

    /// Exports the current game as PGN
    func exportPGN() -> String {
        return session.pgn
    }

    /// Imports a game from FEN
    func importFEN(_ fen: String) {
        fileOperations.loadPosition(from: fen, into: session)
    }

    /// Exports the current position as FEN
    func exportFEN() -> String {
        return session.board.position.fen
    }

    // MARK: - Annotation Methods

    /// Adds a visual annotation to the board
    func addVisualAnnotation(_ annotation: Move.VisualAnnotations) {
        // TODO: Implement visual annotation management
        // This will need to be implemented with a dedicated annotation manager
    }

    /// Removes a visual annotation from the board
    func removeVisualAnnotation(_ annotation: Move.VisualAnnotations) {
        // TODO: Implement visual annotation management
        // This will need to be implemented with a dedicated annotation manager
    }

    /// Gets all visual annotations for the current position
    func getVisualAnnotations() -> [Move.VisualAnnotations] {
        // TODO: Implement visual annotation management
        // This will need to be implemented with a dedicated annotation manager
        return []
    }

    // MARK: - Move Execution Methods

    /// Executes a promotion move
    func executePromotion(to piece: Piece.Kind) {
        guard let move = promotionMove,
              let metaMove = move.metaMove else { return }

        // Promote the piece on the board
        session.board.position.promote(pieceAt: metaMove.end, to: piece)

        // Update the move with promotion information
        var updatedMove = move
        if var updatedMetaMove = updatedMove.metaMove {
            updatedMetaMove.promotion = piece
            updatedMove.metaMove = updatedMetaMove
            session.game.moves.updateMove(at: session.currentMoveIndex, with: updatedMove)
        }

        hidePromotionDialog()
        invalidateCache()
    }

    /// Executes a variation creation with the specified option
    func selectVariationCreationOption(_ option: VariationCreationOption) {
        guard let move = pendingMove,
              let fromIndex = pendingMoveFromIndex else { return }

        switch option {
        case .newVariation:
            if let metaMove = move.metaMove {
                _ = session.makeMove(from: metaMove.start, to: metaMove.end)
            }
        case .newMainLine:
            if let metaMove = move.metaMove {
                if session.makeMove(from: metaMove.start, to: metaMove.end) {
                    _ = session.promoteVariation(at: session.currentMoveIndex)
                }
            }
        case .overwrite:
            let newIndex = session.overwriteMove(move, from: fromIndex)
            session.goToMove(at: newIndex)
        }

        hideVariationDialog()
        invalidateCache()
    }

    // MARK: - Navigation Helper Methods

    /// Checks if can go to previous move
    var canGoToPreviousMove: Bool {
        navigator.canGoToPreviousMove(in: session)
    }

    /// Checks if can go to next move
    var canGoToNextMove: Bool {
        navigator.canGoToNextMove(in: session)
    }

    /// Handles square press for piece selection
    func handleSquarePress(_ square: Square) {
        if let selectedSquare = dragManager.selectedSquare {
            // Try to move to the pressed square
            attemptMove(from: selectedSquare, to: square)
        } else {
            // Select the square if it has a piece
            selectSquare(square)
        }
    }

    /// Validates if a drag can start from the given square
    func validateDragStart(piece: Piece, from square: Square) -> Bool {
        return dragManager.canMovePiece(at: square, in: session)
    }

    /// Cancels reverse drag operation
    func cancelReverseDrag() {
        dragManager.cancelReverseDrag()
    }

    /// Selects a variation from the available options
    func selectVariation(_ option: VariationOption) {
        navigator.selectVariation(option, in: session)
        moveDisplayManager.invalidateCache()
    }

    /// Cancels variation selection
    func cancelVariationSelection() {
        navigator.cancelVariationSelection()
    }

    /// Opens position editor
    func openPositionEditor() {
        fileOperations.openPositionEditor()
    }

    // MARK: - Metadata Binding Methods

    /// Creates a binding for a metadata property
    private func metadataBinding<T>(_ keyPath: WritableKeyPath<Game.Metadata, T>) -> Binding<T> {
        Binding(
            get: {
                self.session.game.metadata[keyPath: keyPath]
            },
            set: {
                self.session.game.metadata[keyPath: keyPath] = $0
                self.session.isModified = true
                self.objectWillChange.send()
            }
        )
    }

    // MARK: - Metadata Bindings
    var eventBinding: Binding<String> { metadataBinding(\.event) }
    var siteBinding: Binding<String> { metadataBinding(\.site) }
    var dateBinding: Binding<String> { metadataBinding(\.date) }
    var roundBinding: Binding<String> { metadataBinding(\.round) }
    var whiteBinding: Binding<String> { metadataBinding(\.white) }
    var blackBinding: Binding<String> { metadataBinding(\.black) }
    var whiteEloBinding: Binding<String> { metadataBinding(\.whiteElo) }
    var blackEloBinding: Binding<String> { metadataBinding(\.blackElo) }
    var whiteFideIdBinding: Binding<String> { metadataBinding(\.whiteFideId) }
    var blackFideIdBinding: Binding<String> { metadataBinding(\.blackFideId) }
    var ecoBinding: Binding<String> { metadataBinding(\.eco) }
    var openingBinding: Binding<String> { metadataBinding(\.opening) }
    var timeControlBinding: Binding<String> { metadataBinding(\.timeControl) }
    var notesBinding: Binding<String> { metadataBinding(\.notes) }
    var setUpBinding: Binding<String> { metadataBinding(\.setUp) }
    var fenBinding: Binding<String> { metadataBinding(\.fen) }

    /// Special binding for result field with default value handling
    var resultBinding: Binding<String> {
        Binding(
            get: {
                let result = self.session.game.metadata.result
                return result.isEmpty ? "*" : result
            },
            set: {
                self.session.game.metadata.result = $0
                self.session.isModified = true
                self.objectWillChange.send()
            }
        )
    }

    /// Auto-detects game result based on current game status
    func autoDetectGameResult() {
        // Only update if result is currently unset
        if session.game.metadata.result.isEmpty || session.game.metadata.result == "*" {
            switch gameStatus {
            case .checkmate(let color):
                session.game.metadata.result = color == .white ? "1-0" : "0-1"
            case .stalemate, .draw:
                session.game.metadata.result = "1/2-1/2"
            case .inProgress:
                session.game.metadata.result = "*"
            }
        }
    }
}
