//
//  ChessMoveExecutor.swift
//  MacChessBase
//
//  Created by AI on 2025/8/9.
//

import SwiftUI
import ChessKit
import AVFoundation

/// Handles chess move execution logic, decoupled from UI concerns
@MainActor
final class ChessMoveExecutor: ObservableObject {
    
    // MARK: - Dependencies
    private let soundManager: SoundManager
    private let engineManager: EngineManager
    
    // MARK: - Move execution callbacks
    var onMoveCompleted: ((Move) -> Void)?
    var onPromotionNeeded: ((Move) -> Void)?
    var onVariationCreationNeeded: ((Move, MoveTree.MoveIndex, MoveTree.MoveIndex?) -> Void)?
    var onGameStatusChanged: ((Move) -> Void)?
    
    // MARK: - Initialization
    init(soundManager: SoundManager? = nil, 
         engineManager: EngineManager? = nil) {
        self.soundManager = soundManager ?? SoundManager.shared
        self.engineManager = engineManager ?? EngineManager.shared
    }
    
    // MARK: - Public Move Execution Interface
    
    /// Attempts to execute a move from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square, in session: GameSession) -> MoveExecutionResult {
        // If clicking the same square, return deselection signal
        if startSquare == endSquare {
            return .shouldDeselect
        }
        
        // If clicking another piece of the same color, return reselection signal
        if let piece = session.board.position.piece(at: endSquare),
           piece.color == session.board.position.sideToMove {
            return .shouldReselect(endSquare)
        }
        
        // Check if the move is legal
        guard session.board.canMove(pieceAt: startSquare, to: endSquare) else {
            return .invalidMove
        }
        
        // Create a temporary move to check for variations
        var tempBoard = session.board
        guard let tempMove = tempBoard.move(pieceAt: startSquare, to: endSquare) else {
            return .invalidMove
        }
        
        // Check if we need to show variation creation dialog
        if shouldShowVariationCreationDialog(for: tempMove, in: session) {
            let existingNextMoveIndex = getExistingNextMoveIndex(for: tempMove, in: session)
            onVariationCreationNeeded?(tempMove, session.currentMoveIndex, existingNextMoveIndex)
            return .variationDialogNeeded
        }
        
        // Execute the move directly
        return executeMove(from: startSquare, to: endSquare, in: session)
    }
    
    /// Executes a move with the specified variation creation option
    func executeMove(_ move: Move, option: VariationCreationOption, from fromIndex: MoveTree.MoveIndex, in session: GameSession) -> Bool {
        guard let metaMove = move.metaMove else {
            return false
        }
        
        var success = false
        var resultMove: Move? = nil
        
        switch option {
        case .newVariation:
            success = session.makeMove(from: metaMove.start, to: metaMove.end)
            if success {
                resultMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
            }
            
        case .newMainLine:
            success = session.makeMove(from: metaMove.start, to: metaMove.end)
            if success {
                resultMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
                _ = session.promoteVariation(at: session.currentMoveIndex)
            }
            
        case .overwrite:
            let newIndex = session.overwriteMove(move, from: fromIndex)
            session.goToMove(at: newIndex)
            success = true
            resultMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
        }
        
        if success, let executedMove = resultMove {
            handleMoveResult(executedMove, in: session)
            return true
        }
        
        return false
    }
    
    /// Completes a pawn promotion
    func completePromotion(move: Move, to pieceKind: Piece.Kind, in session: GameSession) -> Move? {
        let completedMove = session.board.completePromotion(of: move, to: pieceKind)
        
        if let updatedMove = session.game.moves.getNodeMove(index: session.currentMoveIndex) {
            handleMoveResult(completedMove, in: session)
            return completedMove
        }
        
        return nil
    }
    
    // MARK: - Private Implementation
    
    /// Executes a move from coordinates
    private func executeMove(from startSquare: Square, to endSquare: Square, in session: GameSession) -> MoveExecutionResult {
        guard session.makeMove(from: startSquare, to: endSquare) else {
            return .invalidMove
        }
        
        if let actualMove = session.game.moves.getNodeMove(index: session.currentMoveIndex) {
            handleMoveResult(actualMove, in: session)
            return .success(actualMove)
        }
        
        return .invalidMove
    }
    
    /// Handles the result of a successful move execution
    private func handleMoveResult(_ move: Move, in session: GameSession) {
        guard let metaMove = move.metaMove else {
            return
        }
        
        // Play move sound
        soundManager.playMoveSound(for: move)
        
        // Check for pawn promotion
        if metaMove.piece.kind == .pawn &&
           (metaMove.end.rank.value == 8 || metaMove.end.rank.value == 1) &&
           metaMove.promotedPiece == nil {
            onPromotionNeeded?(move)
            return
        }
        
        // Notify about completed move
        onMoveCompleted?(move)
        
        // Check game status and notify if changed
        onGameStatusChanged?(move)
        
        // Auto-analyze new position if engine is running
        if engineManager.state == .analyzing {
            Task.detached(priority: .background) { [weak self] in
                await self?.engineManager.analyzePosition(session.board.position)
            }
        }
    }
    
    /// Checks if a variation creation dialog should be shown
    private func shouldShowVariationCreationDialog(for move: Move, in session: GameSession) -> Bool {
        // Check if there's already a next move that would create a variation
        if session.game.moves.hasNextMove(containing: move, for: session.currentMoveIndex) != nil {
            return false
        }
        
        // Special handling for the first move
        if session.currentMoveIndex == session.game.startingIndex {
            if !session.game.moves.isEmpty {
                return true
            }
        }
        
        // Check if there are existing moves from this position
        let mainLineNextIndex = session.game.moves.nextIndex(currentIndex: session.currentMoveIndex)
        let variations = session.game.moves.variations(from: session.currentMoveIndex)
        
        return mainLineNextIndex != nil || !variations.isEmpty
    }
    
    /// Gets the existing next move index for the current position
    private func getExistingNextMoveIndex(for move: Move, in session: GameSession) -> MoveTree.MoveIndex? {
        if session.currentMoveIndex == session.game.startingIndex {
            return session.game.moves.nextIndex(currentIndex: session.currentMoveIndex)
        }
        return session.game.moves.nextIndex(currentIndex: session.currentMoveIndex)
    }
}

// MARK: - Supporting Types

/// Result of a move execution attempt
enum MoveExecutionResult {
    case success(Move)
    case invalidMove
    case shouldDeselect
    case shouldReselect(Square)
    case variationDialogNeeded
}

/// Options for creating variations when making moves
enum VariationCreationOption {
    case newVariation      // Create new variation (default behavior)
    case newMainLine       // Create new variation and promote it
    case overwrite         // Create new variation and delete existing move
}