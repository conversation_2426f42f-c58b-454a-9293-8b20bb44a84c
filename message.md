refactor: Extract ChessNavigator from ChessGameViewModel and fix variation selection dialog issues

## Major Architecture Refactoring

### ChessNavigator Extraction
- **Created standalone ChessNavigator service**: Decoupled navigation logic from UI concerns
- **Implemented callback-based communication**: Clean separation between navigation logic and view model
- **Reduced ChessGameViewModel complexity**: Removed 150+ lines of navigation code
- **Enhanced testability**: Navigation logic can now be tested independently

### Navigation Logic Migration
- **Moved core methods to ChessNavigator**:
  - `goToPreviousMove()`, `goToNextMove()`, `goToStart()`, `goToEnd()`, `goToMove(at:)` - Core navigation methods
  - `canGoToPreviousMove()`, `canGoToNextMove()` - Navigation state queries
  - `selectVariation()`, `cancelVariationSelection()` - Variation selection handling
  - `showVariationSelectionDialog()`, `generatePGNTextForMove()` - Complex variation dialog logic
- **Preserved all functionality**: Existing behavior maintained through callback pattern
- **Improved separation of concerns**: UI state management vs. navigation logic clearly separated

### Navigation UI State Management
- **Centralized navigation state**: All navigation-related `@Published` properties moved to ChessNavigator
  - `showVariationSelection` - Controls variation selection dialog visibility
  - `availableVariations` - Contains variation options for user selection
  - `isKeyboardNavigationDisabled` - Manages keyboard shortcut state during dialogs
- **Direct UI binding**: Views now bind directly to `navigator` properties instead of computed properties
- **Proper state synchronization**: Navigation state changes immediately trigger UI updates

### Critical Bug Fixes

#### Variation Selection Dialog Not Appearing
- **Root cause**: UI bound to computed properties instead of `@Published` properties
- **Solution**: Updated all Views to bind directly to `viewModel.navigator.showVariationSelection`
- **Impact**: `goToNextMove()` now immediately shows variation dialog when encountering branches

#### UI State Synchronization Issues
- **Root cause**: Missing `onNavigationStateChanged?()` callbacks after state changes
- **Solution**: Added state change notifications in key methods:
  - `showVariationSelectionDialog()` - Notifies UI when dialog appears
  - `cancelVariationSelection()` - Notifies UI when dialog is cancelled
- **Impact**: UI updates are now immediate and reliable

#### SwiftUI Binding Problems
- **Root cause**: Computed properties in ChessGameViewModel prevented SwiftUI change detection
- **Solution**: Removed computed property wrappers, made Views access navigator directly:
  - `viewModel.showVariationSelection` → `viewModel.navigator.showVariationSelection`
  - `viewModel.availableVariations` → `viewModel.navigator.availableVariations`
  - `viewModel.isKeyboardNavigationDisabled` → `viewModel.navigator.isKeyboardNavigationDisabled`
- **Impact**: SwiftUI now properly detects and responds to navigation state changes

### View Updates
- **ChessGameView**: Updated variation selection overlay to bind directly to navigator
- **VariationSelectionView**: Updated all property access to use navigator directly
- **ChessView**: Updated keyboard navigation check to use navigator property

### Architecture Benefits
- **Better maintainability**: Clear separation between UI and navigation logic
- **Improved reusability**: ChessNavigator can be used by other components
- **Enhanced testability**: Independent testing of navigation logic
- **Reduced coupling**: ChessGameViewModel focused purely on UI state management
- **Reliable UI updates**: Direct `@Published` property binding ensures immediate UI response

### Technical Implementation
- **Callback Architecture**: `onNavigationStateChanged` provides UI update notifications
- **Direct Property Access**: Views access navigator properties without computed property indirection
- **State Management**: Navigator manages all navigation-related UI state centrally
- **Memory Safety**: Weak references prevent retain cycles in callback implementations